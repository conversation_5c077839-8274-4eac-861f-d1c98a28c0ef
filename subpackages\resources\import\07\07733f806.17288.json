[1, ["ecpdLyjvZBwrvm+cedCcQy", "30LJR2K6ZCm6IzO7iZfbEG", "f8lQ6xlHJHBq2y0q02rIyy", "bcIyE8keFDxIgJA0KNz0Z8", "94DvEtFyVAGZ0/TI2LaSgd"], ["node", "_textureSetter", "_spriteFrame", "root", "provinceScore", "provinceName", "provincePosition", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs", "_parent"], 2, 2, 9, 4, 5, 7, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "_N$paddingLeft", "node", "_layoutSize"], -1, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_styleFlags", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["b33ecdCtO9Pua+xGvMSklb4", ["node", "provincePosition", "provinceName", "provinceScore"], 3, 1, 1, 1, 1], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6]], [[4, 0, 1, 2, 2], [1, 0, 6, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 1], [5, 0, 1, 2, 5, 6, 4], [6, 0, 2], [1, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 2], [3, 2, 0, 1, 4, 5, 4], [3, 0, 3, 1, 4, 4], [7, 0, 1, 2, 3, 1], [4, 1, 2, 1], [5, 0, 3, 4, 1, 2, 5, 6, 6]], [[[{"name": "rr_item_bg", "rect": [1, 1, 437, 75], "offset": [-0.5, -0.5], "originalSize": [440, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]], [[{"name": "rr_item_rank", "rect": [1, 1, 76, 75], "offset": [-0.5, -0.5], "originalSize": [79, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[[5, "rankRow"], [6, "rankRow", [-7, -8], [[8, 1, 1, 10, -2, [5, 523, 100]], [10, -6, -5, -4, -3]], [11, -1, 0], [5, 523, 100], [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "provinceName", 1, [-11, -12], [[3, -9, [5], 6], [9, 1, 50, 30, -10]], [0, "68kjQSee1Cc7LZPbD8I1Pu", 1, 0], [5, 437, 75], [43, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "provincePosition", 1, [-14], [[3, -13, [1], 2]], [0, "866/Dg0rNOAq7Dqd4iy0+G", 1, 0], [5, 76, 75], [-223.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "position label", 3, [-15], [0, "eaoURLTGlLIqI7TXtNiixJ", 1, 0], [5, 38.93, 50.4]], [12, "34", 35, 1, 1, 1, 4, [0]], [2, "name label", 2, [-16], [0, "b4fscMrxBL3qkD4Oiq2xvt", 1, 0], [5, 80, 50.4], [-128.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "安徽", 1, 1, 6, [3]], [2, "score", 2, [-17], [0, "69YNqHWnhDZrW1mBqmdTeu", 1, 0], [5, 88.98, 50.4], [-14.009999999999998, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "2540", 1, 1, 8, [4]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 9, 0, 5, 7, 0, 6, 5, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 8, 0, 0, 3, 0, -1, 4, 0, -1, 5, 0, -1, 7, 0, -1, 9, 0, 7, 1, 17], [0, 0, 0, 0, 0, 0, 0], [-1, -1, 2, -1, -1, -1, 2], [0, 0, 3, 0, 0, 0, 4]]]]