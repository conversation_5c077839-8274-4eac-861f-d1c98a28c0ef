[1, ["ecpdLyjvZBwrvm+cedCcQy", "0fk54LYI1AsLfsOK6wXZRI", "aes2jAfalNb5ZHW0ZNXr+T", "2cdpG5DH1N6o+FeG4hyp+R", "41pKKWGtpJkIoC5OkvMSZW", "aaDXSyFfdI5bUDyON4fuVT", "03td9B5NhJCr/brUgqdspK", "1bpzEIMj5OULRAsfGD3mYX", "dfhIDfPqVFDYiYFdJWrTr6", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "af4+xVzlNEn7D0Y/QSwGe0", "52xvR6/epHRpvNOM2FKP6H", "84mP7WjSBH0L0UH3MITol6"], ["node", "_spriteFrame", "_textureSetter", "root", "autoPlayLabel", "autoPlayCheckMark", "autoPlayCheckbox", "autoPlayPanel", "_N$target", "data", "_parent", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color"], 0, 4, 5, 9, 1, 7, 2, 5], ["cc.Widget", ["_alignFlags", "_top", "_isAbsTop", "_originalWidth", "_originalHeight", "_right", "_isAbsRight", "alignMode", "_left", "_bottom", "node"], -7, 1], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_fontSize", "_string", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["f391abTnq1JxpHaQjg4D58t", ["node", "autoPlayPanel", "autoPlayCheckbox", "autoPlayCheckMark", "autoPlayLabel"], 3, 1, 1, 1, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingTop", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [0, 0, 6, 8, 5, 3, 4, 7, 2], [3, 0, 1, 2, 3, 4, 3], [12, 0, 1, 2, 3], [0, 0, 6, 5, 3, 4, 7, 2], [3, 1, 2, 3, 4, 2], [6, 0, 1, 2, 2], [4, 0, 2, 3, 4, 5, 6, 7, 6], [13, 0, 1, 2, 2], [0, 0, 8, 5, 3, 4, 7, 2], [0, 0, 6, 5, 3, 4, 2], [1, 0, 1, 2, 10, 4], [7, 0, 2], [0, 0, 6, 8, 5, 3, 4, 2], [0, 0, 1, 2, 6, 5, 3, 4, 4], [0, 0, 6, 8, 3, 4, 7, 2], [0, 0, 6, 8, 5, 3, 9, 4, 7, 2], [0, 0, 1, 6, 5, 3, 4, 7, 3], [8, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 10, 2], [1, 0, 3, 4, 10, 4], [1, 0, 5, 1, 6, 2, 10, 6], [1, 7, 0, 8, 5, 1, 9, 3, 4, 10, 9], [9, 0, 1, 2, 3, 4, 1], [5, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 6, 6], [3, 0, 2, 3, 4, 2], [11, 0, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [4, 1, 0, 2, 3, 4, 5, 6, 7, 7], [4, 1, 0, 6, 7, 3]], [[[{"name": "passBtn", "rect": [1, 5, 332, 103], "offset": [-0.5, 7], "originalSize": [335, 127], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[[12, "PauseUI"], [9, "view", [-8, -9], [[19, 45, -2], [23, -7, -6, -5, -4, -3]], [24, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "container", [-12, -13, -14, -15], [[25, 1, 2, 10, 20, 75, -10, [5, 300, 530]], [11, 1, 0.276122085048011, false, -11]], [0, "10Hol4ihRNOrFhWjG2l8+K", 1, 0], [5, 300, 530], [0, -101.793, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New Sprite", 1, [-17, 2, -18], [[26, 1, -16, [23], 24]], [0, "3cvqNZYbJCOoFbB0qRteJ2", 1, 0], [5, 624, 729]], [14, "bg", 512, 200, 1, [[2, 1, 0, -19, [0], 1], [20, 45, 100, 100, -20], [27, -21]], [0, "a42tBIu8ZDU5XRzOJjjGC0", 1, 0], [5, 750, 1334]], [1, "btn_start", 2, [-24], [[2, 1, 0, -22, [4], 5], [6, 3, -23, [[3, "f391abTnq1JxpHaQjg4D58t", "onClickHome", 1]]]], [0, "054EF6pWFGOZJ4cMLj4Ja3", 1, 0], [5, 200, 75], [0, 217.5, 0, 0, 0, 0, 1, 1.2, 1.2, 0]], [1, "btn_start", 2, [-27], [[2, 1, 0, -25, [7], 8], [6, 3, -26, [[3, "f391abTnq1JxpHaQjg4D58t", "onClickResume", 1]]]], [0, "67NSDlEuBFjIpVW+Go9htD", 1, 0], [5, 200, 75], [0, 67.5, 0, 0, 0, 0, 1, 1.2, 1.2, 0]], [1, "btn_start copy", 2, [-30], [[2, 1, 0, -28, [10], 11], [6, 3, -29, [[3, "f391abTnq1JxpHaQjg4D58t", "onClickPass", 1]]]], [0, "29iThGRHtF4LMmCmu/MbUF", 1, 0], [5, 200, 75], [0, -82.5, 0, 0, 0, 0, 1, 1.2, 1.2, 0]], [15, "AutoPlayPanel", 2, [-31, -32], [0, "a8BvOwfBFCMZFJi3iclfoh", 1, 0], [5, 200, 50], [0, -220, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Checkbox", 8, [-34], [[5, 0, -33, [14], 15]], [0, "c6CvDvKVdHBKD8sbnRpLca", 1, 0], [4, 4278979600], [5, 50, 50], [-92.705, 36.825, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Button", 3, [-38], [[28, 3, -36, [[3, "f391abTnq1JxpHaQjg4D58t", "onClickClose", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -35, 19, 20, 21, 22], [21, 33, -0.03, -0.021152263374485593, false, false, -37]], [0, "3a0T9rLjdPC5NnspzSF5kC", 1, 0], [5, 100, 100], [280.72, 329.92, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Background", 512, 10, [[5, 0, -39, [17], 18], [22, 0, 18, -10, -10, 20, 20, 100, 40, -40]], [0, "33rgeP28RCVYWZamzt2vdy", 1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "New Label", 3, [[29, "暂停", 50, 60, 1, 1, 1, -41, [2]], [11, 17, 0.04, false, -42]], [0, "7b864NwoJK+p9mLcAi7Nuf", 1, 0], [5, 100, 75.6], [0, 297.53999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "New Label", 5, [[7, 30, 35, 1, 1, 1, -43, [3]], [8, 2, -44, [4, 4278190080]]], [0, "ace9cEHzxOC7iBrB+e/45r", 1, 0], [5, 4, 48.1]], [4, "New Label", 6, [[7, 30, 60, 1, 1, 1, -45, [6]], [8, 2, -46, [4, 4278190080]]], [0, "9dsSUhn79CZJfp7GQmUr4H", 1, 0], [5, 4, 79.6], [0, -190.084, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 7, [[7, 30, 60, 1, 1, 1, -47, [9]], [8, 2, -48, [4, 4278190080]]], [0, "76cLCCnOlMoaJy7klZYiHF", 1, 0], [5, 4, 79.6], [0, -112.976, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "CheckMark", 9, [[5, 0, -49, [12], 13]], [0, "62QqhkTNdJ/JbcHWV3qFb6", 1, 0], [5, 50, 50]], [18, "Label", 8, [-50], [0, "538L1sBdpPFqrDrMcXHccJ", 1, 0], [4, 4278190081], [5, 168, 50.4], [33.746, 25.069, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "是否开启自动玩", 24, 17, [16]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 18, 0, 5, 16, 0, 6, 9, 0, 7, 8, 0, 0, 1, 0, -1, 4, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, 0, 3, 0, -1, 12, 0, -3, 10, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 0, 6, 0, -1, 14, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, -1, 9, 0, -2, 17, 0, 0, 9, 0, -1, 16, 0, 8, 11, 0, 0, 10, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 18, 0, 9, 1, 2, 10, 3, 50], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 11, 12, 13, 14, -1, 1], [0, 3, 0, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 7, 0, 8, 0, 0, 1, 1, 9, 10, 11, 0, 12]], [[{"name": "returnBtn", "rect": [1, 5, 332, 103], "offset": [-0.5, 7], "originalSize": [335, 127], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [13]], [[{"name": "continueBtn", "rect": [1, 5, 332, 103], "offset": [-0.5, 7], "originalSize": [335, 127], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [14]]]]