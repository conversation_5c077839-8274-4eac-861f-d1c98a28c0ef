[1, ["ecpdLyjvZBwrvm+cedCcQy", "7ec+EJ5axMIpX12uwsnS6D", "94XM/5efxKjpZJOEF7Fk88", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "cfJU+NuXFAJaVr9nko9MYw", "57HDCyRRVJYohHV6DUH25x", "f2KyzCK9JM3K/c2VLwHqpL", "92vz/QaX9GuYcJ+RHmKHxI", "2erlJvkSNBM5qVMhfd53os"], ["node", "_spriteFrame", "_textureSetter", "root", "checkMark", "charactor", "grayBG", "_N$target", "data", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_children", "_trs", "_parent"], 2, 9, 4, 5, 2, 7, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "node"], -1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_color", "_trs"], 1, 1, 12, 4, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["8146bW1aYJGC5CS3po6rmoC", ["node", "grayBG", "charactor", "checkMark"], 3, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[5, 0, 1, 2, 2], [2, 0, 1, 2, 4, 4], [3, 2, 3, 1], [6, 0, 2], [1, 0, 4, 1, 2, 3, 5, 2], [1, 0, 6, 4, 1, 2, 3, 2], [1, 0, 6, 1, 2, 3, 5, 2], [4, 0, 1, 2, 3, 4, 6, 5, 3], [4, 0, 1, 2, 3, 4, 5, 7, 3], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3, 1], [5, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 3], [10, 0, 1, 2, 3], [2, 3, 0, 1, 2, 4, 5], [2, 0, 4, 2], [3, 1, 0, 2, 3, 4, 3], [3, 0, 2, 3, 2]], [[[{"name": "itemBG", "rect": [0, 0, 160, 170], "offset": [0, 0], "originalSize": [160, 170], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[[3, "skinBtnItem"], [4, "item container", [-6], [[10, -5, -4, -3, -2]], [11, -1, 0], [5, 150, 150], [-202, 206.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Button", 1, [-10, -11, -12, -13], [[12, 1.05, 3, -8, [[13, "8146bW1aYJGC5CS3po6rmoC", "onClick", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -7, 5, 6, 7, 8], [1, 45, 100, 40, -9]], [0, "b0oo6zrDNOzJiXlg/xHZcl", 1, 0], [5, 150, 150]], [6, "Background", 2, [[16, 1, 0, -14, [0], 1], [14, 0, 45, 100, 40, -15]], [0, "90/WCzZNROVoehIfGnzo+2", 1, 0], [5, 150, 150], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "New Sprite(Splash)", false, 2, [[-16, [1, 45, 100, 100, -17]], 1, 4], [0, "7aQU5/+E5Ippce+LyZLe4h", 1, 0], [4, 4290953922], [5, 150, 150]], [8, "check mark", false, 2, [[-18, [15, 36, -19]], 1, 4], [0, "35y6eDYGFNTK9mVkZBEpEh", 1, 0], [5, 60, 61], [45, -44.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 0, 4, [2]], [9, "charactor", 2, [-20], [0, "9c5OXETcZJb76RIRjZdOUu", 1, 0], [4, 4278650632], [5, 96, 112], [0, 0, 0, 0, 0, 0, 1, 0.92, 0.888, 1]], [2, 7, [3]], [2, 5, [4]]], 0, [0, 3, 1, 0, 4, 9, 0, 5, 8, 0, 6, 6, 0, 0, 1, 0, -1, 2, 0, 7, 3, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 7, 0, -4, 5, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, 0, 4, 0, -1, 9, 0, 0, 5, 0, -1, 8, 0, 8, 1, 20], [0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 9], [-1, 1, -1, -1, -1, 9, 10, 11, 12, 1, 1, 1], [0, 1, 0, 0, 0, 1, 3, 4, 5, 6, 7, 8]], [[{"name": "itemGrayBg", "rect": [0, 0, 160, 170], "offset": [0, 0], "originalSize": [160, 170], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [9]], [[{"name": "checkmark", "rect": [4, 4, 60, 61], "offset": [0, -0.5], "originalSize": [68, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [10]]]]