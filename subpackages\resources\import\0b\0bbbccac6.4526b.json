[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "37z4KFT8hJqK7j9lzVqwFV", "a1aUTOq01F55f08IzFAozc", "a6ou5qaZlGn4RB3PAlqnrk", "fdokTAvxxA75S5291yVAf9", "3derjOgRtEyZLWD5+7MBC3"], ["node", "_spriteFrame", "root", "data", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 9, 4, 5, 7, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "_styleFlags", "node", "_materials"], -3, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["c3284X08Q9Nrr6wNBcHROta", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 6, 2, 3, 4, 5, 2], [1, 2, 3, 4, 1], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3], [6, 0, 2], [0, 0, 7, 2, 3, 4, 5, 2], [0, 0, 6, 7, 2, 3, 4, 5, 2], [0, 0, 1, 6, 2, 3, 8, 4, 3], [0, 0, 6, 2, 3, 8, 4, 5, 2], [2, 0, 3, 2], [2, 0, 1, 2, 3, 4], [7, 0, 1], [3, 1, 2, 1], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 4, 3], [4, 0, 3, 4, 1, 2, 6, 7, 6], [4, 0, 5, 1, 2, 6, 7, 5]], [[[[5, "StepOverUI"], [6, "StepOverUI", [-4, -5], [[10, 45, -2], [12, -3]], [13, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg_step", 1, [-7, -8, -9, -10, -11], [[14, 0, -6, [10], 11]], [0, "caVMefSEVMIIT9kZ3f0yRd", 1, 0], [5, 624, 1008], [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 125, 1, [[15, 1, 0, -12, [0], 1], [11, 45, 40, 36, -13]], [0, "d1CikS+0NLmr9RqP6zzCaj", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "btn_add", 2, [[2, -14, [3], 4], [3, 3, -15, [[4, "c3284X08Q9Nrr6wNBcHROta", "onClickAdd", 1]]]], [0, "85QpbxFmFAiKf/iXJOd6li", 1, 0], [5, 288, 96], [0, -166.879, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btn_no", 2, [[2, -16, [5], 6], [3, 3, -17, [[4, "c3284X08Q9Nrr6wNBcHROta", "onClickCancel", 1]]]], [0, "72JQ6IkuJEar1ke/cysubP", 1, 0], [5, 288, 96], [0, -292.294, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "New Label", 2, [[16, "步数补给", 50, 50, 1, 1, -18, [2]]], [0, "15IgVRSmhGhr08XOl6m4Ri", 1, 0], [5, 200, 63], [0, 403.804, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hand", 2, [[2, -19, [7], 8]], [0, "249XJMcItLNrH+Jn08RP4p", 1, 0], [5, 140, 144], [0, 143.627, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "New Label", 2, [[17, "是否补充步数 60 步", 1, 1, 1, -20, [9]]], [0, "2bQFgy+bZNA68l2a0Sqicw", 1, 0], [4, 4278210461], [5, 346.72, 50.4], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -1, 6, 0, -2, 4, 0, -3, 5, 0, -4, 7, 0, -5, 8, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 3, 1, 20], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1], [0, 1, 0, 0, 2, 0, 3, 0, 4, 0, 0, 5]], [[{"name": "hand", "rect": [4, 0, 140, 144], "offset": [2, 0], "originalSize": [144, 144], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [4], [6]]]]