[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2U9fywLRGjKjmP4MIvpDs", "25JpNesARFbZZi0WXU2asD", "a2MjXRFdtLlYQ5ouAFv/+R", "ecQ4jPKjdGkLyB2BY0zJRn", "5dnI+K5dBCPbkFJCEwt2M7", "c5Yu3IqdhFyI0AL154liNE", "5fdKtls2ZDIYGDxiF9H26+", "baSL2JTYhC1a1fcnqcr99q", "06zLeU9rZDHafuXSICi58Y", "96QU/eepZBiZ3LDJ2GKinj"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "collectionCountLabel", "itemContainer", "data", "itemPrefab"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color", "_anchorPoint"], 1, 9, 4, 5, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "_isAbsBottom", "node"], -2, 1], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_N$layoutType", "_N$paddingTop", "_N$spacingY", "_N$paddingLeft", "_N$paddingRight", "_N$paddingBottom", "_N$spacingX", "node", "_layoutSize"], -4, 1, 5], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "_styleFlags", "node", "_materials"], -3, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["2e71eKe1khCxJMjHEqLHmYw", ["node", "itemContainer", "collectionCountLabel", "itemPrefab"], 3, 1, 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "node", "_N$content"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[4, 0, 1, 2, 2], [1, 0, 5, 2], [0, 0, 5, 6, 2, 3, 4, 7, 2], [0, 0, 6, 2, 3, 4, 7, 2], [0, 0, 6, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 3], [3, 2, 3, 4, 1], [7, 0, 2], [0, 0, 5, 6, 2, 3, 4, 2], [0, 0, 2, 3, 4, 7, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [0, 0, 5, 6, 2, 3, 4, 9, 7, 2], [0, 0, 5, 2, 3, 4, 7, 2], [0, 0, 5, 2, 3, 4, 2], [8, 0, 1, 2, 3, 4, 2], [1, 0, 1, 5, 3], [1, 0, 2, 3, 5, 4], [1, 0, 1, 4, 5, 4], [1, 0, 2, 5, 3], [9, 0, 1, 2, 3, 1], [4, 1, 2, 1], [3, 0, 2, 3, 4, 2], [5, 0, 1, 2, 7, 4], [5, 0, 3, 4, 1, 5, 6, 2, 7, 8, 8], [10, 0, 1], [11, 0, 1, 1], [12, 0, 1, 2, 2], [13, 0, 1, 1], [14, 0, 1, 2, 3], [6, 0, 3, 4, 5, 1, 2, 6, 7, 7], [6, 0, 1, 2, 6, 7, 4]], [[[{"name": "myCollectionBG", "rect": [0, 0, 548, 72], "offset": [0, 0], "originalSize": [548, 72], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [1]], [[{"name": "bg", "rect": [5, 5, 732, 1060], "offset": [0, -2], "originalSize": [742, 1066], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[[7, "SkinGalleryUI"], [3, "SkinGalleryUI", [-6, -7], [[1, 45, -2], [19, -5, -4, -3, 13]], [20, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "shop_bg", 1, [-10, -11, -12, -13], [[21, 1, -8, [11], 12], [22, 2, 10, 10, -9]], [0, "54qYued19MDYC1LWvf0U0T", 1, 0], [5, 732, 1060]], [9, "content", [[15, 41, 286.5, -14], [23, 3, 5, 5, 5, 5, 50, 30, -15, [5, 564, 800]]], [0, "32oFGBKf9GmJ0BGl2mWrk6", 1, 0], [5, 564, 800], [0, -113.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg", 125, 1, [[5, 1, 0, -16, [0], 1], [16, 45, 40, 36, -17], [24, -18]], [0, "c5ZGi/qRJFJIQEJNEezam8", 1, 0], [4, 4278190080], [5, 750, 1334]], [4, "title", [-21], [[5, 1, 0, -19, [3], 4], [1, 18, -20]], [0, "18TkAQjhNJIpYW1BOBDNtX", 1, 0], [5, 350, 100]], [3, "New Sprite", [-24], [[6, -22, [6], 7], [17, 4, 0.15, false, -23]], [0, "2aHixI8RpE+79dc+YJgnVf", 1, 0], [5, 548, 72], [0, -27, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "view", [3], [[25, -25, [8]], [1, 45, -26]], [0, "18BoKCo+hKArdaspzKhdyZ", 1, 0], [5, 564, 573]], [2, "title container", 2, [5], [[18, 40, 100, -27]], [0, "c0nTIKFZBK4JHnNUxEbIcw", 1, 0], [5, 732, 200], [0, 420, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "My Collection", 2, [6], [[1, 40, -28]], [0, "78Qv7ajIdLN4GB0SPBZMrM", 1, 0], [5, 732, 180], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "scroll view", 2, [7], [[26, false, -29, 3]], [0, "14J/80TphCS6Ei3jJfuAFU", 1, 0], [5, 564, 573], [3, -166.5, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "return container", 2, [-31], [[1, 40, -30]], [0, "fbkd0omA1A8KFUMRmD+lB7", 1, 0], [5, 732, 180], [0, 0.5, 0], [0, -643, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "return", 11, [[6, -32, [9], 10], [27, -33, [[28, "2e71eKe1khCxJMjHEqLHmYw", "hideUI", 1]]]], [0, "d17EASyZ1NkZ6Q1ipjkQaJ", 1, 0], [5, 220, 86], [0, 53, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New Label", 5, [[29, "我 的 皮 肤", 50, 50, 1, 1, 1, -34, [2]]], [0, "baDEHUvn1IabmeSied80eS", 1, 0], [5, 241.67, 63]], [14, "New Label", 6, [-35], [0, "e8hmjcL85BsakTdeVIhsFE", 1, 0], [5, 260.08, 50.4]], [30, "收藏进度  1/25", 1, 1, 14, [5]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 15, 0, 6, 3, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 0, 6, 0, -1, 14, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -1, 15, 0, 7, 1, 3, 3, 7, 5, 3, 8, 6, 3, 9, 7, 3, 10, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, 8], [0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 7, 8]], [[{"name": "return", "rect": [1, 5, 220, 86], "offset": [-0.5, 6], "originalSize": [223, 108], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [9]], [[{"name": "title", "rect": [0, 0, 118, 56], "offset": [0, 0], "originalSize": [118, 56], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [10]]]]