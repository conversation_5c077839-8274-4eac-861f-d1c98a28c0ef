[1, ["ecpdLyjvZBwrvm+cedCcQy", "2cdpG5DH1N6o+FeG4hyp+R"], ["node", "_spriteFrame", "root", "tipsLabel", "data"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_children", "_parent"], 1, 9, 4, 5, 2, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Label", ["_string", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["node", "_color"], 3, 1, 5], ["6c8f2j79aBOD7tErxPjuVOd", ["node", "tipsLabel"], 3, 1, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 5, 2, 3, 4, 2], [0, 0, 1, 6, 2, 3, 4, 3], [3, 0, 1, 2, 3, 4, 2], [4, 0, 1, 2, 3, 2], [5, 0, 1, 2, 3, 4], [1, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 1, 1], [8, 0, 1, 1]], [[1, "TipsUI"], [2, "view", [-4, -5], [[10, -3, -2]], [7, -1, 0], [5, 750, 110]], [3, "bg", 125, 1, [[5, 0, -6, [0], 1], [6, 45, 40, 36, -7]], [0, "d1CikS+0NLmr9RqP6zzCaj", 1, 0], [5, 750, 110]], [4, "New Label", 1, [[-8, [9, -9, [4, 4284171264]]], 1, 4], [0, "3bvec3QC5FwrCK9IyNoRey", 1, 0], [5, 700, 100]], [8, "哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈", 60, 1, 1, 2, 3, [2]]], 0, [0, 2, 1, 0, 3, 4, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 4, 1, 9], [0, 0, 0], [-1, 1, -1], [0, 1, 0]]