[1, ["ecpdLyjvZBwrvm+cedCcQy", "4bbGx8UR1OvopEEq4tmkgG", "8dEg7fqGRNMKIKkSto3W8J", "2cdpG5DH1N6o+FeG4hyp+R", "b9/n3GSTtCfpmP7T8JzvCH", "a8Y/IKwWtMupcGzYMiRe6/", "a96IL/xA9Nio2jFpq9RdQA", "c1XAZhxhpHM6abMxpQVU7g", "12HMNGBolOk5O4rr6Gr9wg", "1dF4sM9HJEUZWE8mBVEaUo", "70GFiAds1FP5KbUzV51WBV", "a1TE711qBG3Zqu1BZvWrfU", "8b8p32OSlPC7wilppjm7SV", "37r8JdSCRBGJkcMLBq38FU"], ["node", "_spriteFrame", "_textureSetter", "value", "root", "cat", "data", "_defaultClip"], ["cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 1, 9, 4, 5, 7, 1, 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["362d0hkO8dJzLDnPjTBInIr", ["node", "cat"], 3, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "curveData"], 0, 11]], [[4, 0, 1, 2, 2], [2, 0, 6, 2, 3, 4, 5, 2], [1, 0, 2, 3, 4, 2], [1, 2, 3, 4, 1], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3], [5, 0, 2], [2, 0, 7, 2, 3, 4, 5, 2], [2, 0, 1, 6, 2, 3, 4, 3], [6, 0, 1, 2, 3, 4, 5, 2], [3, 0, 3, 2], [3, 0, 1, 2, 3, 4], [7, 0, 1, 1], [4, 1, 2, 1], [1, 0, 1, 2, 3, 4, 3], [1, 2, 3, 1], [10, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4]], [[[[6, "WinUI"], [7, "win", [-5, -6, -7, -8, -9, -10], [[10, 45, -2], [12, -4, -3]], [13, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 180, 1, [[14, 1, 0, -11, [0], 1], [11, 45, 40, 36, -12]], [0, "d1CikS+0NLmr9RqP6zzCaj", 1, 0], [5, 750, 1334]], [1, "btn_start", 1, [[2, 1, -13, [2], 3], [4, 3, -14, [[5, "362d0hkO8dJzLDnPjTBInIr", "onClickStartGame", 1]]]], [0, "46shVZuHVGvajKPYHBxOPZ", 1, 0], [5, 243, 89], [170, -360, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btn_start", 1, [[2, 1, -15, [4], 5], [4, 3, -16, [[5, "362d0hkO8dJzLDnPjTBInIr", "onClickShare", 1]]]], [0, "b7Js58mzFAsaY4uS2EtAVV", 1, 0], [5, 243, 89], [-170, -360, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "effect", 1, [[3, -17, [6], 7], [16, -18, [9], 8]], [0, "b7L7emKPhAup8km5N5Q4hM", 1, 0], [5, 393, 212], [0, 480.936, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "game_win", 1, [[3, -19, [10], 11]], [0, "c4cQf4K5tOxoLubuBxCqI+", 1, 0], [5, 483, 133], [0, 406.469, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "cat_skin_1", 1, [-20], [0, "09gvZsDRZJ0LwdacXY6KWJ", 1, 0], [5, 76, 122], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [15, 7, [12]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 8, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, -6, 7, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 8, 0, 6, 1, 20], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [-1, 1, -1, 1, -1, 1, -1, 1, 7, -1, -1, 1, -1, 1], [0, 3, 0, 4, 0, 5, 0, 1, 2, 2, 0, 6, 0, 7]], [[{"name": "1", "rect": [48, 5, 393, 212], "offset": [0.5, -2.5], "originalSize": [488, 217], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[{"name": "2", "rect": [45, 1, 398, 215], "offset": [0, -0.5], "originalSize": [488, 216], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [9]], [[[17, "win_effect_anim", 0.35, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.16666666666666666}, "value", 6, 1], [{"frame": 0.3333333333333333}, "value", 6, 2]], 11, 11, 11]]]]]], 0, 0, [0, 0, 0], [3, 3, 3], [1, 10, 1]], [[{"name": "btn_share", "rect": [2, 2, 243, 89], "offset": [-0.5, 0.5], "originalSize": [248, 94], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [11]], [[{"name": "game_win", "rect": [2, 1, 483, 133], "offset": [-0.5, 0.5], "originalSize": [488, 136], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [12]], [[{"name": "btn_next", "rect": [2, 2, 243, 89], "offset": [-0.5, 0.5], "originalSize": [248, 94], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [13]]]]