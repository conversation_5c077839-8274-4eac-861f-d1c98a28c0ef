[1, ["ecpdLyjvZBwrvm+cedCcQy", "851kNQu29FxonhbPrbCWKe", "0a/16sralLaK+jzLl7FNqd", "72ipxyd4JCFJus4RrMA4HV", "6aONAubcNA+JWJ4BG7O1zv", "5eafFcNntFgbQVuN5iYEkF", "d0t5w4ViREWamRcHfRJCec", "01bYRtjbBMSLVw8jSUXmW5", "a2MjXRFdtLlYQ5ouAFv/+R", "3cFl2uRAxEfrosOagEoVbF", "ca5HPsVOxEGaFAJD5RPkiO", "e2i0akk2ZCqqxF6ZbbO9st", "3dEx3PlPRDirA5WfaAxAGc", "9fA3quWcRIi57dcEM5Umb6"], ["node", "_spriteFrame", "usingNode", "useNode", "lockNode", "sprite", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], 1, 9, 4, 5, 1, 2, 7, 5], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["247db6xn8RHeKZh4LmusC9i", ["node", "nodes"], 3, 1, 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["660b6nCGldCsIPtFSU07hA1", ["id", "node", "sprite", "lockNode", "useNode", "usingNode"], 2, 1, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[4, 0, 1, 2, 2], [2, 2, 3, 4, 1], [0, 0, 5, 2, 3, 4, 7, 2], [10, 0, 1, 1], [11, 0, 1, 2, 3], [0, 0, 5, 6, 2, 3, 4, 7, 2], [6, 0, 1, 2, 3, 4, 5, 2], [2, 2, 3, 1], [9, 0, 1, 2, 3, 4, 5, 2], [5, 0, 2], [0, 0, 6, 2, 3, 4, 7, 2], [0, 0, 5, 6, 2, 3, 4, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [0, 0, 5, 2, 3, 4, 2], [3, 0, 3, 2], [3, 0, 1, 2, 3, 4], [7, 0, 1, 1], [4, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 3, 4, 3]], [[[{"name": "btn_using", "rect": [6, 5, 128, 43], "offset": [0.5, 0], "originalSize": [139, 53], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [6], [6]], [[{"name": "shop_bg", "rect": [0, 0, 601, 820], "offset": [0, 0], "originalSize": [601, 820], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [6], [7]], [[[9, "ShopUI"], [10, "ShopUI", [-13, -14, -15, -16], [[14, 45, -2], [16, -12, [-3, -4, -5, -6, -7, -8, -9, -10, -11]]], [17, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nodes", 1, [-18, -19, -20, -21, -22, -23, -24, -25, -26], [[18, 1, 3, 10, 20, -17, [5, 560, 580]]], [0, "42ehsqsXhJWL7fYzAAZcj2", 1, 0], [5, 560, 580], [0, -14.035, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-33, -34, -35, -36], [[1, -27, [13], 14], [8, 1, -32, -31, -30, -29, -28]], [0, "85tP+N3WtA9772maHD+si1", 1, 0], [5, 180, 180], [-190, 200, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-43, -44, -45, -46], [[1, -37, [22], 23], [8, 2, -42, -41, -40, -39, -38]], [0, "73buFZI8dK6435hh75EK9v", 1, 0], [5, 180, 180], [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-53, -54, -55, -56], [[1, -47, [31], 32], [8, 3, -52, -51, -50, -49, -48]], [0, "50lDiRXk9M4Zx3KhEOynly", 1, 0], [5, 180, 180], [190, 200, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-63, -64, -65, -66], [[1, -57, [40], 41], [8, 4, -62, -61, -60, -59, -58]], [0, "27Bi7NowtMh4EwUJPX2Dot", 1, 0], [5, 180, 180], [-190, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "ShopSkinItem_1", 2, [-73, -74, -75, -76], [[1, -67, [49], 50], [8, 5, -72, -71, -70, -69, -68]], [0, "3fuxmNdINNebYkg3im0MAx", 1, 0], [5, 180, 180]], [5, "ShopSkinItem_1", 2, [-83, -84, -85, -86], [[1, -77, [58], 59], [8, 6, -82, -81, -80, -79, -78]], [0, "6aceiDGXBKpJpYkkhDEu5O", 1, 0], [5, 180, 180], [190, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-93, -94, -95, -96], [[1, -87, [67], 68], [8, 7, -92, -91, -90, -89, -88]], [0, "57QEQlWyBH/rqrXJ6BdhxV", 1, 0], [5, 180, 180], [-190, -200, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-103, -104, -105, -106], [[1, -97, [76], 77], [8, 8, -102, -101, -100, -99, -98]], [0, "e3FdFJ0d5DbIy7nE7GqvFi", 1, 0], [5, 180, 180], [0, -200, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ShopSkinItem_1", 2, [-113, -114, -115, -116], [[1, -107, [85], 86], [8, 9, -112, -111, -110, -109, -108]], [0, "c3g2an05tF8KRXFbevPmV0", 1, 0], [5, 180, 180], [190, -200, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 3, [[1, -117, [7], 8], [3, -118, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 3]]]], [0, "e95yjXAGpCnJHLhdME+5yY", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 3, [[1, -119, [9], 10], [3, -120, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 3]]]], [0, "8auRKnM4hAMYPpMZoVVi6W", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 3, [[1, -121, [11], 12], [3, -122, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 3]]]], [0, "5cHmi2OvxL3Ioki/GZIZHd", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 4, [[1, -123, [16], 17], [3, -124, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 4]]]], [0, "5exgsMhQ9N1Y84UJ0H1Bv3", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 4, [[1, -125, [18], 19], [3, -126, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 4]]]], [0, "56wUxmhKpGLaxkTL1pqXv0", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 4, [[1, -127, [20], 21], [3, -128, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 4]]]], [0, "aadSE0dIpFBJsAcig3kP47", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 5, [[1, -129, [25], 26], [3, -130, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 5]]]], [0, "f8nSCLwN9EH6cjbknwiLxf", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 5, [[1, -131, [27], 28], [3, -132, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 5]]]], [0, "8781qPrGhGg62pdR2H/9Vc", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 5, [[1, -133, [29], 30], [3, -134, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 5]]]], [0, "2dG3e4gBdKlpsj2z9iF7fz", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 6, [[1, -135, [34], 35], [3, -136, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 6]]]], [0, "53MKCxbPBFU64v7CCcwKrX", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 6, [[1, -137, [36], 37], [3, -138, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 6]]]], [0, "f22dSxAaVIe5UyFc6KDnjg", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 6, [[1, -139, [38], 39], [3, -140, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 6]]]], [0, "b1jIa1SZlF7pNyh9IPaVQx", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 7, [[1, -141, [43], 44], [3, -142, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 7]]]], [0, "67fqRalpVGr4fao+NVGmAE", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 7, [[1, -143, [45], 46], [3, -144, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 7]]]], [0, "31k8po8J5J8LpBTJidVKZf", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 7, [[1, -145, [47], 48], [3, -146, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 7]]]], [0, "da/ot3GN9NdYJz2AtuxPrm", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 8, [[1, -147, [52], 53], [3, -148, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 8]]]], [0, "491fFxT8JDMLfBDyckn+sD", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 8, [[1, -149, [54], 55], [3, -150, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 8]]]], [0, "921iC+AqVN7J9G26l39E5r", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 8, [[1, -151, [56], 57], [3, -152, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 8]]]], [0, "41esCT9ZdFV562HJOTZcn7", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 9, [[1, -153, [61], 62], [3, -154, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 9]]]], [0, "2a4BdZGmNDDZFCfConwCpt", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 9, [[1, -155, [63], 64], [3, -156, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 9]]]], [0, "4fPkUKw4FBY5/XjO8f6vWR", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 9, [[1, -157, [65], 66], [3, -158, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 9]]]], [0, "c2TxDWa/pPw4U7jQ7CMpgk", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 10, [[1, -159, [70], 71], [3, -160, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 10]]]], [0, "44irt4TbpAGqcFBJlL1lmf", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 10, [[1, -161, [72], 73], [3, -162, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 10]]]], [0, "4bAbHhKdlMVIBCEBRtbukh", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 10, [[1, -163, [74], 75], [3, -164, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 10]]]], [0, "cc82hqrFxNkIt17OnZDtsR", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_use", 11, [[1, -165, [79], 80], [3, -166, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 11]]]], [0, "e8K9udVlJLLrdLKw6A88sN", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_using", 11, [[1, -167, [81], 82], [3, -168, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUse", 11]]]], [0, "79Pb7sIZNKZ72XczWUGhs0", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_free_get", 11, [[1, -169, [83], 84], [3, -170, [[4, "660b6nCGldCsIPtFSU07hA1", "onClickUnlock", 11]]]], [0, "4ecoEco0BD3YHYR6Cd8Yaq", 1, 0], [5, 128, 43], [0, -58.599, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg", 125, 1, [[19, 1, 0, -171, [0], 1], [15, 45, 40, 36, -172]], [0, "c5ZGi/qRJFJIQEJNEezam8", 1, 0], [4, 4278190080], [5, 750, 1334]], [2, "btn_close", 1, [[1, -173, [4], 5], [3, -174, [[4, "247db6xn8RHeKZh4LmusC9i", "hideUI", 1]]]], [0, "99iFixy8JIfZyB4g0SPTQ+", 1, 0], [5, 39, 39], [252.783, 354.901, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "shop_bg", 1, [[1, -175, [2], 3]], [0, "54qYued19MDYC1LWvf0U0T", 1, 0], [5, 601, 820]], [6, "cat_skin_1", 3, [-176], [0, "e6A1EPD1hIGrOG39EdDH03", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 42, [6]], [6, "cat_skin_1", 4, [-177], [0, "26jrbOKlpEpIH0ydS8Gt0a", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 44, [15]], [6, "cat_skin_1", 5, [-178], [0, "b9fhBrDNBOioBjoSd+ytTE", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 46, [24]], [6, "cat_skin_1", 6, [-179], [0, "00gmpUek5EDKsadbU6KKTL", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 48, [33]], [6, "cat_skin_1", 7, [-180], [0, "0c3QhgmK9J2LfcWZ0vxJLD", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 50, [42]], [6, "cat_skin_1", 8, [-181], [0, "14kQX5KhVBfZHeYyX/UdMZ", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 52, [51]], [6, "cat_skin_1", 9, [-182], [0, "c390GpFRZETKltvdHCzGe7", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 54, [60]], [6, "cat_skin_1", 10, [-183], [0, "1aAGIQqPFCPKAPo7xCmp12", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 56, [69]], [6, "cat_skin_1", 11, [-184], [0, "555uvpjiNAe5friNG5Xyr1", 1, 0], [5, 48, 86], [0, 12.67, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 58, [78]]], 0, [0, 7, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 10, 0, -9, 11, 0, 0, 1, 0, -1, 39, 0, -2, 41, 0, -3, 40, 0, -4, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 10, 0, -9, 11, 0, 0, 3, 0, 2, 13, 0, 3, 12, 0, 4, 14, 0, 5, 43, 0, 0, 3, 0, -1, 42, 0, -2, 12, 0, -3, 13, 0, -4, 14, 0, 0, 4, 0, 2, 16, 0, 3, 15, 0, 4, 17, 0, 5, 45, 0, 0, 4, 0, -1, 44, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, 0, 5, 0, 2, 19, 0, 3, 18, 0, 4, 20, 0, 5, 47, 0, 0, 5, 0, -1, 46, 0, -2, 18, 0, -3, 19, 0, -4, 20, 0, 0, 6, 0, 2, 22, 0, 3, 21, 0, 4, 23, 0, 5, 49, 0, 0, 6, 0, -1, 48, 0, -2, 21, 0, -3, 22, 0, -4, 23, 0, 0, 7, 0, 2, 25, 0, 3, 24, 0, 4, 26, 0, 5, 51, 0, 0, 7, 0, -1, 50, 0, -2, 24, 0, -3, 25, 0, -4, 26, 0, 0, 8, 0, 2, 28, 0, 3, 27, 0, 4, 29, 0, 5, 53, 0, 0, 8, 0, -1, 52, 0, -2, 27, 0, -3, 28, 0, -4, 29, 0, 0, 9, 0, 2, 31, 0, 3, 30, 0, 4, 32, 0, 5, 55, 0, 0, 9, 0, -1, 54, 0, -2, 30, 0, -3, 31, 0, -4, 32, 0, 0, 10, 0, 2, 34, 0, 3, 33, 0, 4, 35, 0, 5, 57, 0, 0, 10, 0, -1, 56, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, 0, 11, 0, 2, 37, 0, 3, 36, 0, 4, 38, 0, 5, 59, 0, 0, 11, 0, -1, 58, 0, -2, 36, 0, -3, 37, 0, -4, 38, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, -1, 49, 0, -1, 51, 0, -1, 53, 0, -1, 55, 0, -1, 57, 0, -1, 59, 0, 8, 1, 184], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 45, 47, 49, 51, 53, 55, 57, 59], [-1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 8, 0, 9, 0, 10, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 0, 0, 1, 0, 2, 0, 3, 0, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5]], [[{"name": "shop_icon", "rect": [0, 0, 180, 180], "offset": [0, 0], "originalSize": [180, 180], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [6], [11]], [[{"name": "btn_free_get", "rect": [6, 5, 128, 43], "offset": [0.5, 0], "originalSize": [139, 53], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [6], [12]], [[{"name": "btn_use", "rect": [6, 5, 128, 43], "offset": [0.5, 0], "originalSize": [139, 53], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [6], [13]]]]