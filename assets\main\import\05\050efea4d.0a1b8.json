[1, ["ecpdLyjvZBwrvm+cedCcQy", "81irxz13RLR4Nh5yQ5mWQv", "a7PXUbKlhPUqORMAakv1oM", "75hOyMLItDbK6I50hsQz29", "4cUvy87zhO2ZwlEgC91KNT", "7djV9hCU5PDa/q8YX0tADm", "72z1s18GZIJpKab7wm42Vs", "25r/Y9vftCg5LtZ9q5+kcu", "65EUrIQ51E3I4IOeKw+Z7Z", "07OojzdqhHCqb+knrnsh1q", "a1ykLdjlFGuoPtgMFWjFfi"], ["node", "_spriteFrame", "_textureSetter", "progressLabel", "gameVersion", "progressBar", "root", "scene", "_parent"], [["cc.Node", ["_name", "_id", "_obj<PERSON><PERSON>s", "_active", "_opacity", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_prefab"], -2, 9, 5, 1, 7, 2, 5, 4], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 1, 2, 5, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_underlineHeight", "node", "_materials"], -4, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.<PERSON>", ["_fitWidth", "_fitHeight", "node", "_designResolution"], 1, 1, 5], ["21e516K299GEIylKFR2AHC5", ["node", "progressBar", "gameVersion", "progressLabel"], 3, 1, 1, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Scene", ["_name", "_children", "_anchorPoint", "_trs"], 2, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.PrefabInfo", ["root"], 3, 1]], [[0, 0, 7, 5, 6, 8, 2], [2, 2, 3, 4, 1], [5, 0, 3, 2], [6, 0, 1, 3], [0, 0, 1, 9, 5, 6, 8, 3], [0, 0, 7, 9, 5, 6, 2], [0, 0, 2, 7, 5, 6, 3], [0, 0, 3, 7, 5, 10, 6, 8, 3], [0, 0, 7, 5, 6, 2], [0, 0, 4, 7, 5, 6, 8, 3], [0, 0, 7, 11, 2], [3, 0, 2, 3, 4, 7, 5, 2], [3, 0, 2, 3, 6, 4, 5, 2], [3, 0, 1, 2, 3, 6, 4, 5, 3], [7, 0, 1, 2, 3, 3], [5, 0, 1, 2, 3, 4], [8, 0, 1, 2, 3, 1], [2, 0, 2, 3, 4, 2], [2, 1, 0, 2, 3, 4, 3], [2, 1, 0, 2, 3, 3], [4, 0, 1, 2, 5, 3, 4, 7, 8, 7], [4, 0, 6, 7, 8, 3], [4, 0, 1, 2, 3, 4, 7, 8, 6], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 2], [11, 0, 1, 2, 3], [12, 0, 1]], [[[[3, "LoadScene", null], [4, "<PERSON><PERSON>", "a5esZu+45LA5mBpvttspPD", [-3, -4, -5, -6, -7, -8, -9, -10], [[14, true, false, -1, [5, 750, 1334]], [2, 45, -2]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "view", 1, [-16, -17, -18, -19], [[2, 45, -11], [16, -15, -14, -13, -12]], [5, 750, 1334]], [6, "bg", 512, 1, [[17, 0, -20, [0], 1], [15, 45, 100, 100, -21]], [5, 750, 1334]], [7, "New Label", false, 1, [[20, "跑了个羊", 80, 80, 1, 1, 1, -22, [7]], [23, 3, -23, [4, 4294440951]]], [4, 4280130255], [5, 326, 106.8], [0, 400, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "New Node", [1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Main Camera", 1, [[25, 7, -1, -24]], [5, 750, 1334]], [9, "bar1", 218, 2, [[18, 1, 0, -25, [2], 3]], [5, 510, 32], [0, -317.03, 0, 0, 0, 0, 1, 1, -1, 1]], [11, "bar2", 2, [-26], [5, 100, 20], [0, 0, 0.5], [-250, -317.03, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 1, 0, 8, [4]], [12, "desc", 2, [-27], [4, 4278190080], [5, 415.57, 50.4], [0, -233.666, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "正在加载资源,请稍后... ", 2, 10, [5]], [13, "gameVersion", false, 2, [-28], [4, 4278190080], [5, 60, 25.2], [0, 130, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "版本号", 20, 20, 1, 1, 12, [6]], [0, "text_logo", 1, [[1, -29, [8], 9]], [5, 800, 360], [0, 204.042, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [10, "rankUI", 1, [26, -30]], [0, "微信图片_20250226180404", 1, [[1, -31, [10], 11]], [5, 495, 81], [0, -520.419, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [0, "Snipaste_2025-07-01_11-27-33", 1, [[1, -32, [12], 13]], [5, 130, 166], [272.601, -81.78, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 0, 1, 0, 0, 1, 0, -1, 6, 0, -2, 3, 0, -3, 2, 0, -4, 4, 0, -5, 14, 0, -6, 15, 0, -7, 16, 0, -8, 17, 0, 0, 2, 0, 3, 11, 0, 4, 13, 0, 5, 9, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 10, 0, -4, 12, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 6, 0, 0, 7, 0, -1, 9, 0, -1, 11, 0, -1, 13, 0, 0, 14, 0, 6, 15, 0, 0, 16, 0, 0, 17, 0, 7, 5, 1, 8, 5, 32], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [-1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 1], [0, 1, 0, 2, 0, 0, 0, 0, 0, 3, 0, 4, 0, 5, 6]], [[{"name": "bar1", "rect": [0, 0, 102, 24], "offset": [0, 0], "originalSize": [102, 24], "capInsets": [21, 0, 21, 0]}], [1], 0, [0], [2], [7]], [[{"name": "Snipaste_2025-06-06_09-10-09", "rect": [69, 40, 800, 360], "offset": [24.5, -1.5], "originalSize": [889, 437], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[{"name": "Snipaste_2025-07-01_11-27-33", "rect": [8, 1, 130, 166], "offset": [2, 4], "originalSize": [142, 176], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [9]], [[{"name": "bar2", "rect": [0, 0, 102, 32], "offset": [0, 0], "originalSize": [102, 32], "capInsets": [22, 0, 23, 0]}], [1], 0, [0], [2], [10]]]]