[1, ["ecpdLyjvZBwrvm+cedCcQy", "edCXYPqzxDYoEaEgQWlc/Z", "9eEtIkLU1OHZWdPK/Um7X2", "f0BIwQ8D5Ml7nTNQbh1YlS", "2cdpG5DH1N6o+FeG4hyp+R", "3emu+SXJhBX6qqQ6IYRNih", "bcaM5x1C5NYKODiSK3KUh5", "d2ZQcBqTdBcbldudYvIl54", "57HDCyRRVJYohHV6DUH25x", "a4TVumROJHdLCW2jZmFmqL", "dfYOtkW79Dkqv8/u39gAab", "79gUgUWbdIf6fK0XgMaQAe", "61a7/YlZpCaYYHbgJ8ghlR", "b4559wvbhG4Ksazq3Fd/MT", "3fy7HEYodKtZEnIYS1cUJz", "73uKZzRCFDX5VMMNVDCpml", "62/aGY99JBnYi3jGJOaniK", "a6cJB2BA9Mv7PKVjmA/J72", "48GbYSON5DfoC4j0XiIQ5+", "74g/xPpMhDz7Ue6hJaVIJE", "9bKenfEOhEb5uv3VzksnbA", "75teQqt5NMXbjcJMltUu9G", "2bq0v9iypLf7Cw7y4QDPjM", "ebD7IVCHxGBoD1bWwsu2Vo", "feV4zDxVxDhracoy9Z+otW", "d3hD4yNjdIO65ChQFqf32y", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO", "f07CwZHBpJ6YGrYq6nT+fs", "f4pOAkHGZHt6o1OYL01eXe", "331SUfphlFQ4nZEpV19K3s", "53SM8T3AlHAL5NIFz6x7bY", "b4LFZyM7VOSb469epD7tOS", "f2/B14BdtDLZQ5bueBbwUe", "7djm+hWFpOwrs3LwLs6J1c", "23dg7AqQxE2IIeTH5ePGIC", "55teONgABOFYFLVDJRPlPJ", "0e++GeqGpDZ6YfJz2PwjM6"], ["node", "_spriteFrame", "_textureSetter", "_parent", "count", "target", "buff<PERSON><PERSON>", "ad", "root", "animalColorBtn", "comboContainer", "taskProgressLabel", "taskProgressMask", "curLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectNode", "buttonsNode", "weilanNode", "maskNode", "gameBg", "showNode", "cat<PERSON>ontainer", "SpriteNodeInShadow", "SpriteNode", "label", "bar", "data", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], [["cc.Node", ["_name", "_active", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], -1, 4, 9, 5, 1, 7, 2, 5, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_isAbsTop", "_top", "_right", "_isAbsRight", "_originalHeight", "_left", "_bottom", "_isAbsLeft", "_isAbsBottom", "alignMode", "node", "_target"], -9, 1, 1], "cc.SpriteFrame", ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_anchorPoint", "_color", "_children"], 2, 2, 4, 5, 7, 1, 5, 5, 2], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_styleFlags", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_color", "_trs"], 1, 1, 12, 4, 5, 5, 7], ["cc.Layout", ["_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "_resize", "_N$paddingTop", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize", "_N$cellSize"], -5, 1, 5, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 9, 5, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 12, 9, 4, 5, 7], ["5e091tclyBN4rfjM7tfE2sT", ["node", "gameBackgroundArray", "cat<PERSON>ontainer", "showNode", "gameBg", "maskNode", "weilanNode", "buttonsNode", "selectNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "curLevel", "taskProgressMask", "taskProgressLabel", "comboContainer", "animalColorBtn"], 3, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.SafeArea", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["732f6hPCDJHE47dIeacQK0I", ["identifier", "node", "ad", "buff<PERSON><PERSON>", "count"], 2, 1, 1, 1, 1], ["1f3c4JlmDRGaZwO9FwP0y5p", ["node", "SpriteNode", "SpriteNodeInShadow"], 3, 1, 1, 1], ["5a97eULvTRAKI8ytkHF2O/J", ["node", "bar", "label", "count", "tip", "progressContainer"], 3, 1, 1, 1, 1, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.LabelShadow", ["node", "_color"], 3, 1, 5], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["a496aKpVvBNSYlekW4wFu7V", ["node", "colorBtn", "btnLabel", "btnIcon"], 3, 1, 1, 1, 1]], [[9, 0, 1, 2, 2], [5, 2, 3, 4, 1], [1, 0, 4, 3, 5, 2, 12, 6], [0, 0, 7, 5, 4, 6, 8, 2], [14, 0, 1, 2, 3], [0, 0, 7, 9, 5, 4, 6, 8, 2], [0, 0, 1, 7, 9, 5, 4, 6, 8, 3], [3, 0, 5, 1, 2, 3, 2], [5, 0, 2, 3, 4, 2], [8, 0, 1, 2, 2], [5, 0, 2, 3, 2], [15, 0, 1, 2, 3, 4, 2], [4, 0, 3, 4, 1, 2, 6, 7, 6], [0, 0, 7, 5, 4, 6, 2], [18, 0, 1, 2, 2], [4, 0, 3, 5, 1, 2, 6, 7, 6], [0, 0, 9, 5, 4, 6, 8, 2], [1, 0, 8, 12, 13, 3], [5, 1, 0, 2, 3, 4, 3], [4, 0, 4, 1, 2, 6, 7, 5], [0, 0, 7, 9, 5, 4, 6, 10, 8, 2], [3, 0, 5, 1, 2, 3, 6, 4, 2], [3, 0, 5, 1, 2, 3, 4, 2], [6, 0, 2, 3, 4, 6, 5, 7, 2], [1, 0, 3, 2, 12, 4], [1, 0, 1, 6, 12, 4], [19, 0, 1, 1], [21, 0, 1], [4, 0, 3, 5, 4, 1, 2, 6, 7, 7], [10, 0, 2], [0, 0, 1, 9, 5, 4, 6, 8, 3], [0, 0, 9, 4, 6, 8, 2], [0, 0, 7, 4, 6, 8, 2], [0, 0, 9, 5, 4, 8, 2], [0, 0, 7, 9, 5, 4, 8, 2], [0, 0, 7, 9, 5, 4, 6, 10, 2], [0, 0, 1, 7, 9, 4, 3], [0, 0, 1, 2, 7, 5, 4, 11, 6, 8, 4], [0, 0, 1, 2, 5, 4, 6, 8, 12, 4], [0, 0, 7, 9, 5, 4, 6, 2], [0, 0, 1, 2, 7, 5, 4, 11, 6, 4], [0, 0, 3, 7, 5, 4, 6, 3], [0, 0, 2, 7, 5, 4, 6, 3], [0, 0, 1, 7, 5, 4, 6, 3], [0, 0, 3, 7, 9, 4, 8, 12, 3], [3, 0, 8, 1, 2, 3, 4, 2], [3, 0, 5, 1, 2, 7, 3, 6, 4, 2], [3, 0, 5, 1, 2, 7, 3, 4, 2], [11, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4, 5, 3], [6, 0, 2, 3, 4, 5, 2], [1, 0, 12, 2], [1, 0, 7, 4, 1, 12, 5], [1, 0, 2, 12, 3], [1, 0, 4, 5, 12, 4], [1, 0, 1, 12, 3], [1, 0, 3, 2, 12, 13, 4], [1, 0, 3, 12, 13, 3], [1, 0, 7, 4, 3, 8, 9, 5, 2, 10, 1, 6, 12, 12], [1, 11, 0, 1, 6, 12, 5], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [13, 0, 1], [9, 1, 2, 1], [7, 0, 1, 2, 3, 8, 9, 5], [7, 4, 0, 1, 2, 5, 6, 3, 7, 8, 9, 10, 9], [7, 4, 0, 8, 9, 3], [8, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 6, 2], [16, 0, 1, 2, 1], [17, 0, 1, 2, 3, 4, 5, 1], [20, 0, 1, 1], [4, 0, 3, 1, 2, 6, 7, 5], [22, 0, 1, 2, 2], [23, 0, 1, 2, 3, 1]], [[[[29, "GameUI"], [16, "view", [-18, -19, -20, -21, -22, -23, -24], [[51, 45, -2], [60, -16, [77, 78, 79, 80, 81, 82], -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3], [61, -17]], [62, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bottom container", 1, [-27, -28, -29, -30], [[52, 44, 12.873000000000006, -12.873000000000006, 300, -25], [63, 1, 20, 20, 40, -26, [5, 750, 180]]], [0, "55njfknkhJV7/JJriLYJu7", 1, 0], [5, 750, 180], [12.87299999999999, -577, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_ch", 2, [-39, -40], [[1, -31, [54], 55], [9, 3, -33, [[4, "732f6hPCDJHE47dIeacQK0I", "onClickAdReward", -32]]], [17, 4, 31.000000000000007, -34, 1], [11, 0, -38, -37, -36, -35]], [0, "5eI65lo+FDXbeqvZU3pQtg", 1, 0], [5, 144, 118], [-283, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_ts", 2, [-49, -50], [[1, -41, [68], 69], [9, 3, -43, [[4, "732f6hPCDJHE47dIeacQK0I", "onClickAdReward", -42]]], [17, 4, 31.000000000000007, -44, 1], [11, 1, -48, -47, -46, -45]], [0, "22AOgUPG5O9K3YVpYfwDbU", 1, 0], [5, 144, 118], [85, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_fz", 2, [-59, -60], [[1, -51, [75], 76], [9, 3, -53, [[4, "732f6hPCDJHE47dIeacQK0I", "onClickAdReward", -52]]], [17, 4, 31.000000000000007, -54, 1], [11, 2, -58, -57, -56, -55]], [0, "4bPh4JdTJLeo67jq68PYJC", 1, 0], [5, 144, 118], [269, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "btn_fluctuate", [-68, -69], [[8, 0, -61, [8], 9], [9, 3, -63, [[4, "732f6hPCDJHE47dIeacQK0I", "onClickAdReward", -62]]], [11, 4, -67, -66, -65, -64]], [0, "98FoJJoLBFQr5qVyTBO1EY", 1, 0], [5, 88, 88], [32, 51, 0, 0, 0, 0, 1, 1, 1, 0]], [45, "AnimalColorBtn", [-72, -73, -74], [-70, -71], [0, "97rltyeuFFmpPkeyuyAG27", 1, 0], [5, 100, 40], [-442.509, -44.655, 0, 0, 0, 0, 1, 0.862, 2.058, 1.134]], [5, "btn_js", 2, [-82, -83], [[1, -75, [61], 62], [9, 3, -77, [[4, "732f6hPCDJHE47dIeacQK0I", "onClickAdReward", -76]]], [11, 3, -81, -80, -79, -78]], [0, "b7TltzZf5KCL5zEmu+GxN0", 1, 0], [5, 144, 118], [-99, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "left", [-86, 6, -87], [[64, 2, 3, 30, 10, 30, 10, 20, 20, -84, [5, 300, 250], [5, 88, 88]], [53, 9, false, -85]], [0, "b2uTKQY8lLIZJVJqUQsJIE", 1, 0], [5, 300, 250], [-142.5, -85, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "tip", false, [-89, -90, -91], [[65, 1, 1, -88, [5, 124, 200]]], [0, "2a6G89FaBPyao3kifT9HSA", 1, 0], [5, 124, 200], [0, -5.826, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "progress bar", [-92, -93, -94, -95], [0, "f5PydUHw1J9ZXEHUFEXRP4", 1, 0], [5, 250, 82], [-1.253, -26.526, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "right", [[-97, [32, "info_step", -98, [0, "01PuPj3+hPyZLRf5K6SH+i", 1, 0], [5, 180, 70], [0, 58.57, 0, 0, 0, 0, 1, 1, 1, 1]], -99, 7], 1, 4, 1, 1], [[24, 33, 0.05, false, -96]], [0, "67POV6DRdHAIr89SqSMmgf", 1, 0], [5, 250, 250], [332.5, -97.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Task Proress", 12, [-104, -105, -106], [[54, 32, 0.001999999999999995, false, -100], [68, -103, -102, -101]], [0, "d7cRlzjcVNcI6u47BC1kQU", 1, 0], [5, 124, 124], [0, 0.5, 0], [62.5, 13.34, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Top container", 1, [9, -108, 12], [[55, 41, 40, -107]], [0, "cc7aG0qQtIcZM3K/jN8+1N", 1, 0], [5, 750, 250], [0, 0.39, 0.84], [-82.5, 627, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "buff item", false, 6, [-111], [[1, -109, [6], 7], [2, 33, -0.2, -0.2, false, false, -110]], [0, "bagEJSr8dHG5DHrYz0Ln3T", 1, 0], [5, 46, 46], [38.6, 38.6, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "comboHintContainer", [10, 11], [[69, -115, -114, -113, -112, 10, 11]], [0, "18sFYFpQ5Cz4yGddxVbYEu", 1, 0], [0, -72.717, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "info_time", false, 12, [-117, -118, -119], [[56, 1, 0.018500749625187406, false, -116, 1]], [0, "f5LkmRBWhM8pj+XwJgdpQX", 1, 0], [5, 180, 70], [1059.185, 77.82000000000005, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "buff item", false, 3, [-122], [[1, -120, [52], 53], [2, 33, -0.2, -0.2, false, false, -121]], [0, "32AQM7SZ5KTp8Y8YkjLmXj", 1, 0], [5, 46, 46], [38.6, 38.6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "buff item", false, 8, [-125], [[1, -123, [59], 60], [2, 33, -0.2, -0.2, false, false, -124]], [0, "17OulgbotGI6nV47On/4We", 1, 0], [5, 46, 46], [38.6, 38.6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "buff item", false, 4, [-128], [[1, -126, [66], 67], [2, 33, -0.2, -0.2, false, false, -127]], [0, "aeS2KiNE1KMrf4ckDiLWbq", 1, 0], [5, 46, 46], [38.6, 38.6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "buff item", false, 5, [-131], [[1, -129, [73], 74], [2, 33, -0.2, -0.2, false, false, -130]], [0, "9cAV1aBKZPArvjN0QrGyRs", 1, 0], [5, 46, 46], [38.6, 38.6, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ad", 6, [[8, 0, -132, [3], 4], [2, 33, -0.15, -0.2, false, false, -133]], [0, "07gg+LcGlIJI5s54ANAK0X", 1, 0], [5, 40, 40], [37.2, 41.6, 0, 0, 0, 0, 1, 1, 1, 0.5]], [3, "btn_zt", 9, [[8, 0, -134, [10], 11], [9, 3, -135, [[4, "5e091tclyBN4rfjM7tfE2sT", "onClickPause", 1]]], [57, 1, 138, -136, 1]], [0, "e6aSKwHOBMJoqvkrQxSLGA", 1, 0], [5, 88, 88], [-76, -57, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "center", 14, [-138, 16], [[24, 17, 0.3, false, -137]], [0, "cdKNhP2pZPCq8a9l00YYBa", 1, 0], [82.49999999999999, -35, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "label", 11, [[-139, [14, 6, -140, [4, 4278652203]], [26, -141, [4, 4278717998]]], 1, 4, 4], [0, "1exzht9sZKwrVGbnlzOJul", 1, 0], [4, 4278251765], [5, 129.79000000000002, 62.4], [-36.955, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "count", 11, [[-142, [14, 6, -143, [4, 4278652203]], [26, -144, [4, 4278717998]]], 1, 4, 4], [0, "ab18i+AEFGD6e4URYfmHCp", 1, 0], [4, 4278251765], [5, 56.49, 62.4], [67.618, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "progress mask", 13, [-146], [[70, -145, [34]]], [0, "c84YlBG3lLaKCkx4NgVwez", 1, 0], [5, 96, 0], [0, 0.5, 0]], [36, "choose", false, 1, [-147, -148], [0, "f4d52+ZwpBl7I9ssihhO1H", 1, 0]], [37, "showNode", false, 36, 1, [[8, 0, -149, [43], 44], [58, 45, 0.03, 0.03, 0.1652923538230885, 0.11881559220389803, false, false, false, false, 638, 690, -150]], [0, "e94jhtgdtPVZ+a748OAUhy", 1, 0], [4, 4287664272], [5, 705, 955], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "maskNode", false, 100, [[18, 1, 0, -151, [47], 48], [27, -152]], [0, "c5QsVlQbpBqaJfgNa4u90B", 1, 0], [5, 2000, 3000], [0, 0, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, -45]], [3, "ad", 3, [[1, -153, [49], 50], [2, 33, -0.05, -0.05, false, false, -154]], [0, "3dwR7RO1pDXL5ycTPpkGsC", 1, 0], [5, 46, 46], [56.2, 41.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ad", 8, [[1, -155, [56], 57], [2, 33, -0.05, -0.05, false, false, -156]], [0, "b3eLM4DTBGM6KYkWzcUnKp", 1, 0], [5, 46, 46], [56.2, 41.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ad", 4, [[1, -157, [63], 64], [2, 33, -0.05, -0.05, false, false, -158]], [0, "3eQuU1BSJEuZQQ5tzr3wlo", 1, 0], [5, 46, 46], [56.2, 41.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ad", 5, [[1, -159, [70], 71], [2, 33, -0.05, -0.05, false, false, -160]], [0, "a3xHcxPIBPL5gnNKaJJPQF", 1, 0], [5, 46, 46], [56.2, 41.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "bg", 512, 1, [[-161, [25, 45, 100, 100, -162]], 1, 4], [0, "82nHYmy7hNmLw5boeme2a6", 1, 0], [5, 750, 1334]], [3, "btn_change_bg", 9, [[8, 0, -163, [1], 2], [66, -164, [[4, "5e091tclyBN4rfjM7tfE2sT", "onClickChangeBgBtn", 1]]]], [0, "55yL2XIf5G8onidKafki5B", 1, 0], [5, 88, 88], [-76, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "level_bg", 24, [-166], [[1, -165, [13], 14]], [0, "14QpHuXnFKP6CCAWx0w5iz", 1, 0], [5, 190, 66]], [50, "New Label", 37, [[-167, [14, 1.5, -168, [4, 4285101568]]], 1, 4], [0, "d2P5pjuS9H8I8nigFgdXoC", 1, 0], [5, 113.04, 40.8]], [5, "New Sprite", 10, [-170], [[1, -169, [16], 17]], [0, "e8p2TwlzRPELQtqvQ6NddX", 1, 0], [5, 45, 44], [-39.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Sprite", 10, [-172], [[1, -171, [21], 22]], [0, "fbguaHVn5DqZOupSJUqTd4", 1, 0], [5, 45, 44], [39.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "bar", 11, [-173, -174], [0, "8b9as2rz5LoInKqYLz3Izl", 1, 0], [5, 260, 85], [0, 0, 0.5], [-130, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "New Sprite", false, 100, 17, [[8, 0, -175, [28], 29], [25, 45, 190, 66, -176]], [0, "0b0pmlfTxBMaWQrc2nhzV0", 1, 0], [4, **********], [5, 180, 70]], [41, "Background", 512, 7, [[18, 1, 0, -177, [36], 37], [59, 0, 45, 100, 40, -178]], [0, "d6IXSFhPxNqqs0FSYE60j7", 1, 0], [5, 100, 40]], [42, "maskNode", 100, 28, [[18, 1, 0, -179, [40], 41], [27, -180]], [0, "83Jvr2Av5GRZiOUm+AEpHZ", 1, 0], [5, 2000, 3000]], [3, "New Label", 28, [[19, "请选择一个目标", 1, 1, 1, -181, [42]], [14, 2, -182, [4, 4278190080]]], [0, "9dY6MAhutAVr1k2ZklmQ1X", 1, 0], [5, 284, 54.4], [0, -649.004, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "weilan", false, 1, [[1, -183, [45], 46]], [0, "e6dWWIUQtOS6+sLwswGVxX", 1, 0], [5, 526, 772]], [44, "rogn<PERSON>", 512, 1, [30], [0, "19A34AJN1JroOJMh+8sP14", 1, 0], [0, 0, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, 45]], [10, 0, 35, [0]], [7, "count", 15, [-184], [0, "4cukYkPDZFWrUqfLdkpk1F", 1, 0], [5, 16.68, 50.4]], [12, "0", 30, 1, 1, 1, 49, [5]], [28, "第 34 关", 30, 30, 1, 1, 1, 38, [12]], [13, "New Label", 39, [[15, "1", 35, 35, 1, 1, -185, [15]]], [0, "0bo1SifM1GKbsDDuAXqGU0", 1, 0], [5, 19.47, 44.1]], [13, "New Sprite", 10, [[1, -186, [18], 19]], [0, "b02WMUKe5GZomd3NoR6f0z", 1, 0], [5, 34, 5]], [13, "New Label", 40, [[15, "2", 35, 35, 1, 1, -187, [20]]], [0, "2dBykksl1LPLEugTbeaFem", 1, 0], [5, 19.47, 44.1]], [13, "bg", 11, [[1, -188, [23], 24]], [0, "01AqUf+hhAa6q7QvNDWkgA", 1, 0], [5, 266, 90]], [10, 0, 41, [25]], [72, 260, 41, 56], [19, "连击 X", 1, 1, 1, 25, [26]], [19, "12", 1, 1, 1, 26, [27]], [3, "New Label", 17, [[15, "时间", 30, 30, 1, 1, -189, [30]]], [0, "17ine4S5dG2roZne/phzgk", 1, 0], [5, 60, 37.8], [-43.443, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "New Label", 17, [-190], [0, "23KEIcttxC2qAfCLNTYWw1", 1, 0], [5, 75.07, 37.8], [33.222, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "12:12", 30, 30, 1, 1, 61, [31]], [46, "alpaca copy", 13, [-191], [0, "be7WtsOt5FKYaTq93dsIDg", 1, 0], [4, **********], [5, 124, 124], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [10, 0, 63, [32]], [21, "alpaca", 27, [-192], [0, "00azMsxL1FK5UHRcL9uZ7t", 1, 0], [5, 124, 124], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 0.8]], [10, 0, 65, [33]], [47, "progress label", 13, [-193], [0, "04VjBClnxHGKXGomASWy9R", 1, 0], [4, **********], [5, 96.71, 25.2], [-1.708, -30.583, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "进度 100%", 20, 20, 1, 1, 1, 67, [35]], [22, "New Label", 7, [-194], [0, "5e8sBIFNpIYK8C0YAF01qy", 1, 0], [5, 60, 50.4], [-0.914, 0, 0, 0, 0, 0, 1, 0.708, 0.708, 0.708]], [71, "颜色钻", 20, 1, 1, 69, [38]], [7, "New Node", 7, [-195], [0, "a2+lhoI/pMFI6JxVO75s74", 1, 0], [5, 110, 45]], [10, 0, 71, [39]], [67, 2, 7, [[4, "a496aKpVvBNSYlekW4wFu7V", "onClick", 7]], [4, **********], [4, **********], [4, **********], 1], [73, 7, 73, 70, 72], [7, "count", 18, [-196], [0, "efdDq9Re1GzLVcnn6xD7hZ", 1, 0], [5, 16.68, 50.4]], [12, "0", 30, 1, 1, 1, 75, [51]], [7, "count", 19, [-197], [0, "82IMnPJqFAKbbXOUhWujPO", 1, 0], [5, 16.68, 50.4]], [12, "0", 30, 1, 1, 1, 77, [58]], [7, "count", 20, [-198], [0, "88ERWRj0JGO6RB2T8ZAVWv", 1, 0], [5, 16.68, 50.4]], [12, "0", 30, 1, 1, 1, 79, [65]], [7, "count", 21, [-199], [0, "bbnvKTd+dKZKTQkKx/yCaP", 1, 0], [5, 16.68, 50.4]], [12, "0", 30, 1, 1, 1, 81, [72]]], 0, [0, 8, 1, 0, 0, 1, 0, 9, 74, 0, 10, 16, 0, 11, 68, 0, 12, 27, 0, 13, 51, 0, 14, 62, 0, 15, 28, 0, 16, 2, 0, 17, 46, 0, 18, 30, 0, 19, 48, 0, 20, 29, 0, 21, 47, 0, 0, 1, 0, 0, 1, 0, -1, 35, 0, -2, 14, 0, -3, 28, 0, -4, 29, 0, -5, 46, 0, -6, 47, 0, -7, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 8, 0, -3, 4, 0, -4, 5, 0, 0, 3, 0, 5, 3, 0, 0, 3, 0, 0, 3, 0, 4, 76, 0, 6, 18, 0, 7, 31, 0, 0, 3, 0, -1, 31, 0, -2, 18, 0, 0, 4, 0, 5, 4, 0, 0, 4, 0, 0, 4, 0, 4, 80, 0, 6, 20, 0, 7, 33, 0, 0, 4, 0, -1, 33, 0, -2, 20, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 4, 82, 0, 6, 21, 0, 7, 34, 0, 0, 5, 0, -1, 34, 0, -2, 21, 0, 0, 6, 0, 5, 6, 0, 0, 6, 0, 4, 50, 0, 6, 15, 0, 7, 22, 0, 0, 6, 0, -1, 22, 0, -2, 15, 0, -1, 73, 0, -2, 74, 0, -1, 43, 0, -2, 69, 0, -3, 71, 0, 0, 8, 0, 5, 8, 0, 0, 8, 0, 4, 78, 0, 6, 19, 0, 7, 32, 0, 0, 8, 0, -1, 32, 0, -2, 19, 0, 0, 9, 0, 0, 9, 0, -1, 36, 0, -3, 23, 0, 0, 10, 0, -1, 39, 0, -2, 53, 0, -3, 40, 0, -1, 55, 0, -2, 41, 0, -3, 25, 0, -4, 26, 0, 0, 12, 0, -1, 17, 0, 3, 12, 0, -3, 13, 0, 0, 13, 0, 22, 64, 0, 23, 66, 0, 0, 13, 0, -1, 63, 0, -2, 27, 0, -3, 67, 0, 0, 14, 0, -2, 24, 0, 0, 15, 0, 0, 15, 0, -1, 49, 0, 4, 59, 0, 24, 58, 0, 25, 57, 0, 0, 16, 0, 0, 17, 0, -1, 42, 0, -2, 60, 0, -3, 61, 0, 0, 18, 0, 0, 18, 0, -1, 75, 0, 0, 19, 0, 0, 19, 0, -1, 77, 0, 0, 20, 0, 0, 20, 0, -1, 79, 0, 0, 21, 0, 0, 21, 0, -1, 81, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 37, 0, -1, 58, 0, 0, 25, 0, 0, 25, 0, -1, 59, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, -1, 65, 0, -1, 44, 0, -2, 45, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, -1, 48, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, -1, 38, 0, -1, 51, 0, 0, 38, 0, 0, 39, 0, -1, 52, 0, 0, 40, 0, -1, 54, 0, -1, 56, 0, -2, 57, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, -1, 50, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 60, 0, -1, 62, 0, -1, 64, 0, -1, 66, 0, -1, 68, 0, -1, 70, 0, -1, 72, 0, -1, 76, 0, -1, 78, 0, -1, 80, 0, -1, 82, 0, 26, 1, 6, 3, 9, 7, 3, 12, 9, 3, 14, 10, 3, 16, 11, 3, 16, 12, 3, 14, 16, 3, 24, 30, 3, 47, 199], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 56, 64, 66, 72, 73, 73, 73, 73], [-1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -2, -3, -4, -5, -6, 1, 1, 1, 1, 1, 27, 28, 29, 30], [0, 0, 9, 0, 1, 0, 0, 2, 0, 10, 0, 11, 0, 0, 5, 0, 0, 6, 0, 12, 0, 0, 6, 0, 13, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 4, 0, 0, 4, 0, 14, 0, 4, 0, 1, 0, 0, 2, 0, 15, 0, 1, 0, 0, 2, 0, 16, 0, 1, 0, 0, 2, 0, 17, 0, 1, 0, 0, 2, 0, 18, 19, 7, 20, 21, 22, 23, 7, 24, 8, 8, 25, 3, 26, 3, 27]], [[{"name": "btn_inverse", "rect": [0, 0, 144, 118], "offset": [0, 0], "originalSize": [144, 118], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [28]], [[{"name": "smallStick", "rect": [0, 0, 34, 5], "offset": [0, 0], "originalSize": [34, 5], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [29]], [[{"name": "btn_addTime", "rect": [0, 0, 144, 118], "offset": [0, 0], "originalSize": [144, 118], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [30]], [[{"name": "btn_undo", "rect": [0, 0, 144, 118], "offset": [0, 0], "originalSize": [144, 118], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [31]], [[{"name": "circleIcon", "rect": [0, 0, 46, 46], "offset": [0, 0], "originalSize": [46, 46], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [32]], [[{"name": "comboBg_1", "rect": [0, 0, 266, 90], "offset": [0, 0], "originalSize": [266, 90], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [33]], [[{"name": "squreIcon", "rect": [0, 0, 45, 44], "offset": [0, 0], "originalSize": [45, 44], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [34]], [[{"name": "纯色模式", "rect": [0, 4, 497, 482], "offset": [-3.5, 2.5], "originalSize": [504, 495], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [35]], [[{"name": "adRewardIcon", "rect": [0, 0, 46, 46], "offset": [0, 0], "originalSize": [46, 46], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [36]], [[{"name": "comboBg_2", "rect": [4, 4, 258, 82], "offset": [0, 0], "originalSize": [266, 90], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [37]]]]