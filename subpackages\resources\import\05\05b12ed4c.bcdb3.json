[1, ["d4JjqHS5RHf7MbKRchgBZA", "c6kLA51WxFyZa/8rI9RIVD", "36e6VVoUBDxbZYSCkN/euI", "d3xqlz9ndO0rJHQYPyK58e", "78J0j22m5PiI6frIxz5iG7", "8dBj/1IalAS5AT4Q9IX4AP", "90FAk0qdFLkoCX+vdB/nvP", "d1oksu/dlLJost3Tx/fmLT", "d4XcRMoA1GrKrXAT6/w8ED", "653B3SXEVF97pSn78Zw+q/", "f7xJVK/kRCC7f6hfHRmCrM"], ["_textureSetter", "1", "2", "3", "4", "5", "6", "7", "8", "9", "hit"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "2", "rect": [305, 0, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "9", "rect": [915, 540, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "panda_color.plist", [{}, "1", 6, 0, "2", 6, 1, "3", 6, 2, "4", 6, 3, "5", 6, 4, "6", 6, 5, "7", 6, 6, "8", 6, 7, "9", 6, 8, "hit", 6, 9]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]], [[{"name": "4", "rect": [915, 0, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "5", "rect": [1220, 0, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "6", "rect": [0, 540, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "1", "rect": [0, 0, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "7", "rect": [305, 540, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "3", "rect": [610, 0, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "8", "rect": [610, 540, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "hit", "rect": [1220, 540, 305, 540], "offset": [0, 0], "originalSize": [305, 540], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]