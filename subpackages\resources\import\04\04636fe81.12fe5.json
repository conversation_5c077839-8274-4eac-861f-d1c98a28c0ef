[1, ["ecpdLyjvZBwrvm+cedCcQy", "0fk54LYI1AsLfsOK6wXZRI", "2cdpG5DH1N6o+FeG4hyp+R", "a2MjXRFdtLlYQ5ouAFv/+R", "99zjkPF/dLu5YJ3oOhcAr7", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "af4+xVzlNEn7D0Y/QSwGe0", "a6cJB2BA9Mv7PKVjmA/J72", "39RXYUpU1HMogMTAc7L8V8"], ["node", "_spriteFrame", "root", "buff<PERSON><PERSON><PERSON><PERSON><PERSON>", "buffItemSprite", "buffItemName", "_N$target", "data", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_textureSetter"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], 0, 9, 4, 5, 1, 2, 7, 5], ["cc.Widget", ["_alignFlags", "_top", "_isAbsTop", "_originalWidth", "_originalHeight", "_right", "_isAbsRight", "alignMode", "_left", "_bottom", "node"], -7, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_color", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["3ee18WrITJCbb7E4irLToSQ", ["node", "buffItemName", "buffItemSprite", "buff<PERSON><PERSON><PERSON><PERSON><PERSON>"], 3, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "node"], -1, 1]], [[4, 0, 1, 2, 2], [0, 0, 6, 7, 3, 4, 5, 8, 2], [2, 0, 1, 2, 3, 4, 3], [2, 1, 2, 3, 4, 2], [11, 0, 1, 2, 3], [12, 0, 1, 2, 3, 4, 5, 6, 7, 7], [7, 0, 2], [0, 0, 7, 3, 4, 5, 8, 2], [0, 0, 6, 7, 3, 4, 5, 2], [0, 0, 1, 2, 6, 3, 4, 5, 4], [0, 0, 1, 6, 3, 4, 5, 8, 3], [0, 0, 6, 7, 3, 4, 9, 5, 8, 2], [0, 0, 6, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 5, 4, 6, 7, 2], [1, 0, 10, 2], [1, 1, 2, 10, 3], [1, 0, 3, 4, 10, 4], [1, 0, 5, 1, 6, 2, 10, 6], [1, 7, 0, 8, 5, 1, 9, 3, 4, 10, 9], [1, 0, 1, 2, 10, 4], [9, 0, 1, 2, 3, 1], [4, 1, 2, 1], [2, 0, 2, 3, 4, 2], [2, 2, 3, 1], [10, 0, 1], [5, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5]], [[[[6, "BuffItemUI"], [7, "view", [-7, -8], [[16, 45, -2], [22, -6, -5, -4, -3]], [23, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Sprite", 1, [-10, -11, -12], [[24, 1, -9, [15], 16]], [0, "3cvqNZYbJCOoFbB0qRteJ2", 1, 0], [5, 624, 729]], [1, "container", 2, [-14, -15, -16], [[17, 0.5146090534979423, false, -13]], [0, "10Hol4ihRNOrFhWjG2l8+K", 1, 0], [5, 300, 120], [0, -173.581, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 512, 200, 1, [[2, 1, 0, -17, [0], 1], [18, 45, 100, 100, -18], [26, -19]], [0, "a42tBIu8ZDU5XRzOJjjGC0", 1, 0], [5, 750, 1334]], [1, "btn_reward", 3, [-22], [[2, 1, 0, -20, [7], 8], [27, 3, -21, [[4, "3ee18WrITJCbb7E4irLToSQ", "onClickAdReward", 1]]]], [0, "67NSDlEuBFjIpVW+Go9htD", 1, 0], [5, 220, 80], [0, -69.067, 0, 0, 0, 0, 1, 1.2, 1.2, 0]], [1, "New Button", 2, [-26], [[28, 3, -24, [[4, "3ee18WrITJCbb7E4irLToSQ", "onClickClose", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -23, 11, 12, 13, 14], [19, 33, -0.03, -0.021152263374485593, false, false, -25]], [0, "3a0T9rLjdPC5NnspzSF5kC", 1, 0], [5, 100, 100], [280.72, 329.92, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Background", 512, 6, [[3, 0, -27, [9], 10], [20, 0, 18, -10, -10, 20, 20, 100, 40, -28]], [0, "33rgeP28RCVYWZamzt2vdy", 1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "buff item name", 2, [[-29, [21, 17, 0.04, false, -30]], 1, 4], [0, "7b864NwoJK+p9mLcAi7Nuf", 1, 0], [5, 100, 75.6], [0, 297.53999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Sprite(Splash)", 3, [-32], [[3, 0, -31, [4], 5]], [0, "d6BHg0PaVK4KYhcikql+jy", 1, 0], [4, 4289838734], [5, 170, 160], [0, 274.624, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "New Label", 5, [[5, "观看广告获取", 30, 60, 1, 1, 1, -33, [6]], [29, 2, -34, [4, 4278190080]]], [0, "9dsSUhn79CZJfp7GQmUr4H", 1, 0], [5, 184, 79.6]], [5, "暂停", 50, 60, 1, 1, 1, 8, [2]], [14, "New Sprite", 9, [-35], [0, "bdEjgOt4NHU51kvojvIVHk", 1, 0], [5, 144, 118]], [25, 12, [3]], [15, "New label", 3, [-36], [0, "16BDc0F4lAbpJ4jZaLFMpc", 1, 0], [4, 4282861383], [5, 450, 90.39999999999999], [0, 0.5, 1], [0, 156.23400000000004, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "<b>11</b><br/>", 1, 30, 450, 14]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 15, 0, 4, 13, 0, 5, 11, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, -1, 8, 0, -2, 3, 0, -3, 6, 0, 0, 3, 0, -1, 9, 0, -2, 14, 0, -3, 5, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 6, 7, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, -1, 11, 0, 0, 8, 0, 0, 9, 0, -1, 12, 0, 0, 10, 0, 0, 10, 0, -1, 13, 0, -1, 15, 0, 7, 1, 36], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13], [-1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, 8, 9, 10, 11, -1, 1, 1], [0, 2, 0, 0, 0, 3, 0, 0, 4, 0, 1, 1, 5, 6, 7, 0, 8, 9]], [[{"name": "btnGreenBg", "rect": [0, 0, 190, 66], "offset": [0, 0], "originalSize": [190, 66], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [12], [10]]]]