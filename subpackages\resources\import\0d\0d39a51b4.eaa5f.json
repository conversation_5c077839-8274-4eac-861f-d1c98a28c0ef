[1, ["ecpdLyjvZBwrvm+cedCcQy", "24aG+UiW9MA4SPsOMYwh8V", "a2MjXRFdtLlYQ5ouAFv/+R", "6cuovKmXlN1beJyS/MH93o", "ca5HPsVOxEGaFAJD5RPkiO"], ["node", "_spriteFrame", "_textureSetter", "root", "view", "data"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], 1, 9, 4, 5, 1, 2, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["5bcfexuak9EfJ8KL1AMkbBG", ["node", "view"], 3, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.SubContextView", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4], [2, 2, 3, 4, 1], [4, 0, 2], [0, 0, 6, 2, 3, 4, 7, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [0, 0, 5, 6, 2, 3, 4, 2], [0, 0, 5, 2, 3, 4, 7, 2], [5, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 1], [1, 1, 2, 1], [2, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3], [10, 0, 1]], [[[{"name": "rank_bg", "rect": [0, 0, 600, 960], "offset": [0, 0], "originalSize": [600, 960], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [1]], [[[3, "rankUI"], [4, "rankUI", [-5, -6, -7], [[1, 45, 750, 1334, -2], [9, -4, -3]], [10, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 125, 1, [[11, 1, 0, -8, [0], 1], [1, 45, 40, 36, -9]], [0, "273mE7VLhPTrjbO5YM6POi", 1, 0], [4, 4278190080], [5, 750, 1334]], [6, "rank_bg", 1, [-11], [[2, -10, [2], 3]], [0, "13ETQk4VBJwKSeKGNUKPXx", 1, 0], [5, 600, 960]], [7, "btn_close", 1, [[2, -12, [4], 5], [12, 3, -13, [[13, "5bcfexuak9EfJ8KL1AMkbBG", "hideUI", 1]]]], [0, "a7R+XXZCpPbI7KzV0HIFyi", 1, 0], [5, 39, 39], [260.174, 420.75, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "view", 3, [-14], [0, "30B2p3sqZCcYbsQzS2Lm7X", 1, 0], [5, 580, 850], [0, -52.174, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 5]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 6, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, 0, 4, 0, -1, 6, 0, 5, 1, 14], [0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1], [0, 2, 0, 3, 0, 4]]]]